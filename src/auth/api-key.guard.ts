import { Injectable, CanActivate, ExecutionContext, UnauthorizedException } from '@nestjs/common';
import { ApiKeyService } from 'src/api-key/api-key.service';

@Injectable()
export class ApiKeyAuthGuard implements CanActivate {
  constructor(private apiKeyService: ApiKeyService) {}

  // ExecutionContext represents the current context of the execution 
  // (e.g., whether the request comes from HTTP, WebSocket, gRPC, etc.).
  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    
    const apiKey = request.headers['x-api-key'];

    if (!apiKey) {
      throw new UnauthorizedException('API key required');
    }

    // Validate API key
    const keyData = await this.apiKeyService.getUserFromApiKey(apiKey);

    request.user = {
      id: keyData.user_id,
      apiKeyId: keyData.id
    };
  

    return true;
  }
}