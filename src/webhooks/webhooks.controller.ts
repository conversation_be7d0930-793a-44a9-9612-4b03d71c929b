import { Controller, Get, Post, Body, Patch, Param, Delete, UseGuards } from '@nestjs/common';
import { WebhooksService } from './webhooks.service';
import { CreateWebhookDto } from './dto/create-webhook.dto';
import { UpdateWebhookDto } from './dto/update-webhook.dto';
import { ApiSecurity } from '@nestjs/swagger';
import { ApiKeyAuthGuard } from 'src/auth/api-key.guard';

@Controller('webhooks')
@UseGuards(ApiKeyAuthGuard)
export class WebhooksController {
  constructor(private readonly webhooksService: WebhooksService) {}

  @Post('create')
  @ApiSecurity('x-api-key')
  async create(@Body() createWebhookDto: CreateWebhookDto) {
    return this.webhooksService.createWebhook(createWebhookDto);
  }
}
