import { ApiProperty } from "@nestjs/swagger";
import { <PERSON>Array, <PERSON>Enum, IsInt, IsNotEmpty, IsString } from "class-validator";

enum WebhookStatus {
  ACTIVE = 'active',
  PAUSED = 'paused', 
  TERMINATED = 'terminated',
  RESUMING = 'resuming'
}

export class CreateWebhookDto {
  @ApiProperty({ example: 1})
  @IsNotEmpty()
  @IsInt()
  user_id: number;

  @ApiProperty({ example: "banana rama"})
  @IsNotEmpty()
  @IsString()
  webhook_name: string;

  @ApiProperty({ example: "ethereum-mainnet"})
  @IsNotEmpty()
  @IsString()
  network: string;

  @ApiProperty({ example: "https://mydomain.com/webhook-endpoint"})
  @IsNotEmpty()
  @IsString()
  webhook_url: string;

  @ApiProperty({ example: "none" })
  @IsNotEmpty()
  @IsString()
  compression: string;

  @ApiProperty({ example: "active", enum: ["active", "paused","terminated","resuming"] })
  @IsEnum(["active", "paused","terminated","resuming"])
  @IsNotEmpty()
  status: string;

  @ApiProperty({ example: ["0xabc123...","0xdef456..."] })
  @IsNotEmpty()
  @IsArray()
  @IsString({ each: true })
  wallets: string[];
}